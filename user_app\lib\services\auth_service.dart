import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';
import 'settings_service.dart';
import 'referral_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<UserModel?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String username,
    required String displayName,
  }) async {
    try {
      // Check if username is already taken
      final usernameQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('username', isEqualTo: username)
          .get();

      if (usernameQuery.docs.isNotEmpty) {
        throw Exception('Username is already taken');
      }

      // Create user with Firebase Auth
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = result.user;
      if (user != null) {
        // Check if user approval is required
        final requiresApproval = await SettingsService.isUserApprovalRequired();

        // Create user document in Firestore
        final UserModel userModel = UserModel(
          id: user.uid,
          email: email,
          username: username,
          displayName: displayName,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          registrationStatus: requiresApproval
              ? RegistrationStatus.pending
              : RegistrationStatus.approved,
          metadata: const {},
        );

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .set(userModel.toMap());

        return userModel;
      }
    } catch (e) {
      throw Exception('Failed to sign up: ${e.toString()}');
    }
    return null;
  }

  // Sign up with extended information
  Future<UserModel?> signUpWithExtendedInfo({
    required String email,
    required String password,
    required String username,
    required String displayName,
    String? mobile,
    String? bio,
    String? gender,
    String? address,
    String? country,
    String? companyName,
    String? profileImageUrl,
    String? referralCode,
  }) async {
    try {
      // Check if username is already taken
      final usernameQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('username', isEqualTo: username)
          .get();

      if (usernameQuery.docs.isNotEmpty) {
        throw Exception('Username is already taken');
      }

      // Create user with Firebase Auth
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = result.user;
      if (user != null) {
        // Check if user approval is required
        final requiresApproval = await SettingsService.isUserApprovalRequired();

        // Generate referral code for new user
        final userReferralCode = ReferralService.generateReferralCode();

        // Create user document in Firestore with extended info
        final UserModel userModel = UserModel(
          id: user.uid,
          email: email,
          username: username,
          displayName: displayName,
          profileImageUrl: profileImageUrl,
          bio: bio,
          gender: gender,
          mobile: mobile,
          address: address,
          country: country,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          registrationStatus: requiresApproval
              ? RegistrationStatus.pending
              : RegistrationStatus.approved,
          // Add custom fields
          metadata: {
            'companyName': companyName,
            'referralCode': userReferralCode,
            'usedReferralCode': referralCode,
          },
        );

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .set(userModel.toMap());

        return userModel;
      }
    } catch (e) {
      throw Exception('Failed to sign up: ${e.toString()}');
    }
    return null;
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = result.user;
      if (user != null) {
        // Get user data from Firestore
        final DocumentSnapshot userDoc = await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          final isActive = userData['isActive'] ?? true;

          // Check if user account is active
          if (!isActive) {
            // Sign out the user immediately
            await _auth.signOut();
            throw Exception('Your account is no longer available. Please contact support for assistance.');
          }

          return UserModel.fromDocument(userDoc);
        }
      }
    } catch (e) {
      throw Exception('Failed to sign in: ${e.toString()}');
    }
    return null;
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final User? user = currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      // Re-authenticate user with current password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'wrong-password':
            throw Exception('Current password is incorrect');
          case 'weak-password':
            throw Exception('New password is too weak');
          case 'requires-recent-login':
            throw Exception('Please sign in again to change your password');
          default:
            throw Exception('Failed to change password: ${e.message}');
        }
      } else {
        throw Exception('Failed to change password: ${e.toString()}');
      }
    }
  }

  // Get current user data
  Future<UserModel?> getCurrentUserData() async {
    try {
      final User? user = currentUser;
      if (user != null) {
        final DocumentSnapshot userDoc = await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .get();

        if (userDoc.exists) {
          return UserModel.fromDocument(userDoc);
        }
      }
    } catch (e) {
      throw Exception('Failed to get user data: ${e.toString()}');
    }
    return null;
  }

  // Update user profile
  Future<void> updateUserProfile({
    required String userId,
    String? displayName,
    String? username,
    String? bio,
    String? profileImageUrl,
    String? coverImageUrl,
    String? gender,
    String? address,
    String? country,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (displayName != null) updateData['displayName'] = displayName;
      if (username != null) {
        // Get current user data to check if username is actually changing
        final currentUserDoc = await _firestore
            .collection(AppConstants.usersCollection)
            .doc(userId)
            .get();

        final currentUsername = currentUserDoc.data()?['username'] as String?;

        // Only check for username uniqueness if it's actually changing
        if (currentUsername != username) {
          final usernameQuery = await _firestore
              .collection(AppConstants.usersCollection)
              .where('username', isEqualTo: username)
              .get();

          if (usernameQuery.docs.isNotEmpty) {
            throw Exception('Username is already taken');
          }
        }
        updateData['username'] = username;
      }
      if (bio != null) updateData['bio'] = bio;
      if (profileImageUrl != null) updateData['profileImageUrl'] = profileImageUrl;
      if (coverImageUrl != null) updateData['coverImageUrl'] = coverImageUrl;
      if (gender != null) updateData['gender'] = gender;
      if (address != null) updateData['address'] = address;
      if (country != null) updateData['country'] = country;

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(updateData);
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  // Follow/Unfollow user
  Future<void> toggleFollowUser({
    required String currentUserId,
    required String targetUserId,
  }) async {
    try {
      final batch = _firestore.batch();

      final currentUserRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(currentUserId);
      
      final targetUserRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(targetUserId);

      final currentUserDoc = await currentUserRef.get();
      final currentUserData = UserModel.fromDocument(currentUserDoc);

      if (currentUserData.isFollowing(targetUserId)) {
        // Unfollow
        batch.update(currentUserRef, {
          'following': FieldValue.arrayRemove([targetUserId]),
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
        
        batch.update(targetUserRef, {
          'followers': FieldValue.arrayRemove([currentUserId]),
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
      } else {
        // Follow
        batch.update(currentUserRef, {
          'following': FieldValue.arrayUnion([targetUserId]),
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
        
        batch.update(targetUserRef, {
          'followers': FieldValue.arrayUnion([currentUserId]),
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to toggle follow: ${e.toString()}');
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String query,
    int limit = 20,
  }) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('username', isGreaterThanOrEqualTo: query.toLowerCase())
          .where('username', isLessThan: '${query.toLowerCase()}z')
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final DocumentSnapshot userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (userDoc.exists) {
        return UserModel.fromDocument(userDoc);
      }
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
    return null;
  }

  // Submit reseller application
  Future<bool> submitResellerApplication({
    required String userId,
    required String reason,
  }) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'resellerApplicationStatus': ResellerApplicationStatus.pending.value,
        'resellerApplicationDate': Timestamp.fromDate(DateTime.now()),
        'resellerApplicationReason': reason,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
      return true;
    } catch (e) {
      throw Exception('Failed to submit reseller application: ${e.toString()}');
    }
  }

  // Submit enhanced reseller application with additional fields
  Future<bool> submitEnhancedResellerApplication({
    required String userId,
    required String resellerName,
    required String resellerNumber,
    required String resellerAddress,
    required String nationalIdUrl,
    required String tradeLicenseUrl,
    String? resellerReferId,
    String? socialMediaLink,
    String? referrerName,
    String? referrerNumber,
    String? referrerAddress,
    String? referrerNationalIdUrl,
  }) async {
    try {
      final updateData = {
        'resellerApplicationStatus': ResellerApplicationStatus.pending.value,
        'resellerApplicationDate': Timestamp.fromDate(DateTime.now()),
        'resellerApplicationReason': 'Enhanced application with complete details',
        'updatedAt': Timestamp.fromDate(DateTime.now()),

        // Enhanced Reseller Registration Fields
        'resellerName': resellerName,
        'resellerReferId': resellerReferId,
        'resellerNumber': resellerNumber,
        'resellerAddress': resellerAddress,
        'resellerNationalIdUrl': nationalIdUrl,
        'resellerTradeLicenseUrl': tradeLicenseUrl,
        'resellerSocialMediaLink': socialMediaLink,

        // Referrer Information
        'referrerName': referrerName,
        'referrerNumber': referrerNumber,
        'referrerAddress': referrerAddress,
        'referrerNationalIdUrl': referrerNationalIdUrl,
      };

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(updateData);

      return true;
    } catch (e) {
      throw Exception('Failed to submit enhanced reseller application: ${e.toString()}');
    }
  }

  // Update profile with extended information
  Future<UserModel?> updateProfileWithExtendedInfo({
    String? displayName,
    String? username,
    String? bio,
    String? gender,
    String? address,
    String? country,
    String? mobile,
    String? companyName,
  }) async {
    try {
      final User? user = _auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // Check if username is already taken (if username is being changed)
      if (username != null) {
        final usernameQuery = await _firestore
            .collection(AppConstants.usersCollection)
            .where('username', isEqualTo: username)
            .where('id', isNotEqualTo: user.uid)
            .get();

        if (usernameQuery.docs.isNotEmpty) {
          throw Exception('Username is already taken');
        }
      }

      // Get current user data
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        throw Exception('User document not found');
      }

      final currentUserData = userDoc.data()!;
      final currentMetadata = Map<String, dynamic>.from(currentUserData['metadata'] ?? {});

      // Update metadata with new mobile and company info
      if (mobile != null) {
        currentMetadata['mobile'] = mobile;
      }
      if (companyName != null) {
        currentMetadata['companyName'] = companyName;
      }

      // Prepare update data
      final Map<String, dynamic> updateData = {
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'metadata': currentMetadata,
      };

      if (displayName != null) updateData['displayName'] = displayName;
      if (username != null) updateData['username'] = username;
      if (bio != null) updateData['bio'] = bio;
      if (gender != null) updateData['gender'] = gender;
      if (address != null) updateData['address'] = address;
      if (country != null) updateData['country'] = country;

      // Update user document
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .update(updateData);

      // Get updated user data
      final updatedDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      return UserModel.fromDocument(updatedDoc);
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }
}
