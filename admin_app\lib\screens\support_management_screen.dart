import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/support_model.dart';
import '../models/user_model.dart';
import '../services/support_service.dart';
import '../services/user_management_service.dart';
import '../widgets/support_detail_dialog.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class SupportManagementScreen extends StatefulWidget {
  const SupportManagementScreen({super.key});

  @override
  State<SupportManagementScreen> createState() => _SupportManagementScreenState();
}

class _SupportManagementScreenState extends State<SupportManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<SupportModel> _supportRequests = [];
  List<String> _categories = [];
  bool _isLoading = false;
  bool _hasMoreRequests = true;
  DocumentSnapshot? _lastDocument;
  SupportStatus? _currentStatusFilter;
  String _selectedCategory = 'all';
  String _searchQuery = '';

  // Bulk operations
  Set<String> _selectedRequests = {};
  bool _isSelectionMode = false;

  // Statistics
  Map<String, int> _statistics = {
    'total': 0,
    'pending': 0,
    'inProgress': 0,
    'resolved': 0,
    'closed': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(_onTabChanged);
    _scrollController.addListener(_onScroll);
    _loadSupportRequests();
    _loadCategories();
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) return;

    setState(() {
      _currentStatusFilter = switch (_tabController.index) {
        0 => null, // All
        1 => SupportStatus.pending,
        2 => SupportStatus.inProgress,
        3 => SupportStatus.resolved,
        4 => SupportStatus.closed,
        _ => null, // Default case
      };
    });
    _refreshRequests();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMoreRequests) {
      _loadMoreRequests();
    }
  }

  Future<void> _loadSupportRequests() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final requests = await SupportService.getSupportRequests(
        statusFilter: _currentStatusFilter,
        categoryFilter: _selectedCategory,
      );

      final lastDoc = requests.isNotEmpty
          ? await FirebaseFirestore.instance
              .collection('support_requests')
              .doc(requests.last.id)
              .get()
          : null;

      setState(() {
        _supportRequests = requests;
        _hasMoreRequests = requests.length >= 20;
        _lastDocument = lastDoc;
      });
    } catch (e) {
      print('Error loading support requests: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreRequests() async {
    if (_isLoading || !_hasMoreRequests) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final requests = await SupportService.getSupportRequests(
        lastDocument: _lastDocument,
        statusFilter: _currentStatusFilter,
        categoryFilter: _selectedCategory,
      );

      final lastDoc = requests.isNotEmpty
          ? await FirebaseFirestore.instance
              .collection('support_requests')
              .doc(requests.last.id)
              .get()
          : _lastDocument;

      setState(() {
        _supportRequests.addAll(requests);
        _hasMoreRequests = requests.length >= 20;
        _lastDocument = lastDoc;
      });
    } catch (e) {
      print('Error loading more requests: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshRequests() async {
    setState(() {
      _supportRequests.clear();
      _lastDocument = null;
      _hasMoreRequests = true;
      _selectedRequests.clear();
      _isSelectionMode = false;
    });
    await _loadSupportRequests();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await SupportService.getCategories();
      setState(() {
        _categories = ['all', ...categories];
      });
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await SupportService.getSupportStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  void _toggleRequestSelection(String requestId) {
    setState(() {
      if (_selectedRequests.contains(requestId)) {
        _selectedRequests.remove(requestId);
      } else {
        _selectedRequests.add(requestId);
      }

      if (_selectedRequests.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedRequests.clear();
      }
    });
  }

  void _selectAllRequests() {
    setState(() {
      if (_selectedRequests.length == _supportRequests.length) {
        _selectedRequests.clear();
      } else {
        _selectedRequests = _supportRequests.map((r) => r.id).toSet();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          if (_isSelectionMode) _buildBulkActionsBar(),
          Expanded(
            child: _buildRequestsList(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Support Management',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppConstants.primaryColor,
      elevation: 0,
      actions: [
        if (_isSelectionMode) ...[
          IconButton(
            icon: const Icon(Icons.select_all, color: Colors.white),
            onPressed: _selectAllRequests,
            tooltip: 'Select All',
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: _toggleSelectionMode,
            tooltip: 'Cancel Selection',
          ),
        ] else ...[
          IconButton(
            icon: const Icon(Icons.checklist, color: Colors.white),
            onPressed: _toggleSelectionMode,
            tooltip: 'Bulk Operations',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _refreshRequests,
            tooltip: 'Refresh',
          ),
        ],
      ],
    );
  }




  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(text: 'All (${_statistics['total']})'),
          Tab(text: 'Pending (${_statistics['pending']})'),
          Tab(text: 'In Progress (${_statistics['inProgress']})'),
          Tab(text: 'Resolved (${_statistics['resolved']})'),
          Tab(text: 'Closed (${_statistics['closed']})'),
        ],
      ),
    );
  }



  Widget _buildBulkActionsBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.primaryColor.withOpacity(0.1),
      child: Row(
        children: [
          Text(
            '${_selectedRequests.length} selected',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppConstants.primaryColor,
            ),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _selectedRequests.isNotEmpty ? () => _bulkUpdateStatus(SupportStatus.inProgress) : null,
            icon: const Icon(Icons.work, size: 16),
            label: const Text('Mark In Progress'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          ElevatedButton.icon(
            onPressed: _selectedRequests.isNotEmpty ? () => _bulkUpdateStatus(SupportStatus.resolved) : null,
            icon: const Icon(Icons.check_circle, size: 16),
            label: const Text('Mark Resolved'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestsList() {
    if (_isLoading && _supportRequests.isEmpty) {
      return const LoadingWidget();
    }

    if (_supportRequests.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.support_agent,
        title: 'No Support Requests',
        subtitle: 'No support requests found matching your criteria.',
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshRequests,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _supportRequests.length + (_hasMoreRequests ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _supportRequests.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingLarge),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final request = _supportRequests[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: _buildRequestCard(request),
          );
        },
      ),
    );
  }

  Future<void> _bulkUpdateStatus(SupportStatus status) async {
    if (_selectedRequests.isEmpty) return;

    try {
      final success = await SupportService.bulkUpdateStatus(
        ids: _selectedRequests.toList(),
        status: status,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_selectedRequests.length} requests updated to ${status.displayName}'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        _refreshRequests();
        _loadStatistics();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update requests'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _showRequestDetails(SupportModel request) {
    showDialog(
      context: context,
      builder: (context) => SupportDetailDialog(
        request: request,
        onRequestUpdated: () {
          _loadSupportRequests();
          _loadStatistics();
        },
      ),
    );
  }

  Widget _buildRequestCard(SupportModel request) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        onTap: _isSelectionMode
            ? () => _toggleRequestSelection(request.id)
            : () => _showRequestDetails(request),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with user avatar, status and selection
              Row(
                children: [
                  if (_isSelectionMode)
                    Checkbox(
                      value: _selectedRequests.contains(request.id),
                      onChanged: (selected) => _toggleRequestSelection(request.id),
                    ),
                  // User Avatar
                  FutureBuilder<UserModel?>(
                    future: _getUserFromRequest(request),
                    builder: (context, snapshot) {
                      final user = snapshot.data;
                      return CircleAvatar(
                        radius: 20,
                        backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                        backgroundImage: user?.profileImageUrl != null
                            ? NetworkImage(user!.profileImageUrl!)
                            : null,
                        child: user?.profileImageUrl == null
                            ? Icon(
                                Icons.person,
                                color: AppConstants.primaryColor,
                                size: 20,
                              )
                            : null,
                      );
                    },
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            _buildStatusChip(request.status),
                            const SizedBox(width: AppConstants.paddingSmall),
                            Text(
                              request.category,
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeSmall,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Request #${request.id.substring(0, 8)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    request.formattedCreatedAt,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // Request details
              if (request.referenceId != null) ...[
                Row(
                  children: [
                    const Icon(Icons.badge, size: 16, color: AppConstants.textSecondaryColor),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'Ref ID: ${request.displayReferenceId}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingSmall),
              ],

              Row(
                children: [
                  const Icon(Icons.phone, size: 16, color: AppConstants.textSecondaryColor),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    request.contactNumber,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingSmall),

              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: AppConstants.textSecondaryColor),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      request.address,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // Message preview
              Text(
                request.message,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              if (request.hasAdminNote) ...[
                const SizedBox(height: AppConstants.paddingMedium),
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.admin_panel_settings, size: 16, color: AppConstants.primaryColor),
                      const SizedBox(width: AppConstants.paddingSmall),
                      Expanded(
                        child: Text(
                          'Admin Note: ${request.adminNote}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.primaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<UserModel?> _getUserFromRequest(SupportModel request) async {
    try {
      // First try to get user by reference ID if available
      if (request.referenceId != null && request.referenceId!.isNotEmpty) {
        return await UserManagementService.getUserById(request.referenceId!);
      }

      // If no reference ID, try to find user by mobile number
      if (request.contactNumber.isNotEmpty) {
        final users = await UserManagementService.getUsersByMobile(request.contactNumber);
        if (users.isNotEmpty) {
          return users.first;
        }
      }

      return null;
    } catch (e) {
      print('Error getting user from request: $e');
      return null;
    }
  }

  Widget _buildStatusChip(SupportStatus status) {
    Color color;
    switch (status) {
      case SupportStatus.pending:
        color = Colors.orange;
        break;
      case SupportStatus.inProgress:
        color = Colors.purple;
        break;
      case SupportStatus.resolved:
        color = Colors.green;
        break;
      case SupportStatus.closed:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
