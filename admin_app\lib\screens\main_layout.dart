import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/navigation_models.dart';
import '../services/navigation_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/password_change_dialog.dart';
import '../services/notification_service.dart';
import 'settings_screen.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final Function(String)? onNavigationItemTapped;

  const MainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
    this.onNavigationItemTapped,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  bool _isDrawerOpen = false; // Changed to false - drawer closed by default
  final double _drawerWidth = 260;

  // Check if the screen is mobile size
  bool get _isMobile => MediaQuery.of(context).size.width < 768;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ensure drawer is closed by default on mobile
    if (_isMobile && _isDrawerOpen) {
      setState(() {
        _isDrawerOpen = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final navigationService = Provider.of<NavigationService>(context);
    final navigationGroups = navigationService.getNavigationItems();
    final currentItem = navigationService.getAllNavigationItems().firstWhere(
          (item) => item.route == widget.currentRoute,
          orElse: () => const NavigationItem(
            icon: Icons.error_outline,
            activeIcon: Icons.error,
            label: 'Error',
            index: -1,
          ),
        );

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Main content area - always full width
            Column(
              children: [
                // Top App Bar
                Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        _isDrawerOpen ? Icons.close : Icons.menu,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      onPressed: () {
                        setState(() {
                          _isDrawerOpen = !_isDrawerOpen;
                        });
                      },
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      currentItem.activeIcon,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      currentItem.label,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    // User profile and notifications
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        final user = authProvider.currentUser;
                        if (user == null) return const SizedBox.shrink();

                        return StreamBuilder<int>(
                          stream: NotificationService.getUnreadNotificationsCountStream(user.id),
                          builder: (context, snapshot) {
                            final unreadCount = snapshot.data ?? 0;

                            return Stack(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.notifications_outlined),
                                  onPressed: () {
                                    Navigator.pushNamed(context, '/admin-notifications');
                                  },
                                ),
                                if (unreadCount > 0)
                                  Positioned(
                                    right: 8,
                                    top: 8,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.error,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 16,
                                        minHeight: 16,
                                      ),
                                      child: Text(
                                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(width: 8),
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        final user = authProvider.currentUser;
                        return PopupMenuButton<String>(
                          icon: CircleAvatar(
                            radius: 16,
                            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                            child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                                ? ClipOval(
                                    child: Image.network(
                                      user.profileImageUrl!,
                                      width: 32,
                                      height: 32,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Icon(
                                          Icons.person,
                                          size: 18,
                                          color: Theme.of(context).primaryColor,
                                        );
                                      },
                                    ),
                                  )
                                : Icon(
                                    Icons.person,
                                    size: 18,
                                    color: Theme.of(context).primaryColor,
                                  ),
                          ),
                          onSelected: (value) async {
                            switch (value) {
                              case 'profile':
                                _showPasswordChangeDialog(context);
                                break;
                              case 'admin_settings':
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const SettingsScreen(),
                                  ),
                                );
                                break;
                              case 'logout':
                                final shouldLogout = await showDialog<bool>(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: const Text('Sign Out'),
                                    content: const Text('Are you sure you want to sign out?'),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: const Text('Cancel'),
                                      ),
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        style: TextButton.styleFrom(
                                          foregroundColor: Theme.of(context).colorScheme.error,
                                        ),
                                        child: const Text('Sign Out'),
                                      ),
                                    ],
                                  ),
                                );

                                if (shouldLogout == true) {
                                  try {
                                    await authProvider.signOut();
                                    if (context.mounted) {
                                      Navigator.of(context).pushReplacementNamed('/login');
                                    }
                                  } catch (e) {
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text('Error signing out: $e')),
                                      );
                                    }
                                  }
                                }
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'profile',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.person_outline,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                  const SizedBox(width: 12),
                                  const Text('Profile Settings'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'admin_settings',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.admin_panel_settings,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                  const SizedBox(width: 12),
                                  const Text('Admin Settings'),
                                ],
                              ),
                            ),
                            const PopupMenuDivider(),
                            PopupMenuItem(
                              value: 'logout',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.logout,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'Sign Out',
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.error,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
              // Page Content
              Expanded(
                child: widget.child,
              ),
            ],
          ),

          // Navigation Drawer - overlay style
          if (_isDrawerOpen)
            Stack(
              children: [
                // Background overlay
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isDrawerOpen = false;
                    });
                  },
                  child: Container(
                    color: Colors.black26,
                  ),
                ),
                // Drawer content
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  left: 0,
                  top: 0,
                  bottom: 0,
                  width: _drawerWidth,
                  child: Material(
                    elevation: 16,
                    shadowColor: Colors.black.withOpacity(0.5),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.surface,
                            Theme.of(context).colorScheme.surface.withOpacity(0.95),
                          ],
                        ),
                      ),
                      child: Column(
                        children: [
                          // Drawer Header
                          Container(
                            height: 100,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Theme.of(context).colorScheme.primary,
                                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
                                ],
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.admin_panel_settings,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Admin Panel',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        'Management System',
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Navigation Items
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: ListView.builder(
                                itemCount: navigationGroups.length,
                                itemBuilder: (context, groupIndex) {
                                  final group = navigationGroups[groupIndex];
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      if (group.title.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(20, 20, 20, 8),
                                          child: Text(
                                            group.title.toUpperCase(),
                                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                              color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 1.2,
                                            ),
                                          ),
                                        ),
                                      ...group.items.map((item) {
                                        final isSelected = item.route == widget.currentRoute;
                                        return Container(
                                          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(12),
                                            color: isSelected
                                                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                                : Colors.transparent,
                                          ),
                                          child: ListTile(
                                            leading: Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(8),
                                                color: isSelected
                                                    ? Theme.of(context).colorScheme.primary
                                                    : Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                              ),
                                              child: Icon(
                                                isSelected ? item.activeIcon : item.icon,
                                                color: isSelected
                                                    ? Colors.white
                                                    : Theme.of(context).colorScheme.primary,
                                                size: 20,
                                              ),
                                            ),
                                            title: Text(
                                              item.label,
                                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                color: isSelected
                                                    ? Theme.of(context).colorScheme.primary
                                                    : Theme.of(context).textTheme.bodyLarge?.color,
                                                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                              ),
                                            ),
                                            onTap: () {
                                              if (item.route != null) {
                                                // Close drawer after navigation
                                                setState(() {
                                                  _isDrawerOpen = false;
                                                });

                                                if (widget.onNavigationItemTapped != null) {
                                                  widget.onNavigationItemTapped!(item.route!);
                                                } else {
                                                  Navigator.of(context).pushReplacementNamed(item.route!);
                                                }
                                              }
                                            },
                                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                            dense: true,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      if (groupIndex < navigationGroups.length - 1)
                                        Container(
                                          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                                          height: 1,
                                          color: Theme.of(context).dividerColor.withOpacity(0.3),
                                        ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),

                          // Drawer Footer
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: Theme.of(context).dividerColor.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                final user = authProvider.currentUser;
                                return Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 20,
                                      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                      child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                                          ? ClipOval(
                                              child: Image.network(
                                                user.profileImageUrl!,
                                                width: 40,
                                                height: 40,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error, stackTrace) {
                                                  return Icon(
                                                    Icons.person,
                                                    color: Theme.of(context).colorScheme.primary,
                                                  );
                                                },
                                              ),
                                            )
                                          : Icon(
                                              Icons.person,
                                              color: Theme.of(context).colorScheme.primary,
                                            ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            user?.displayName ?? user?.email ?? 'Admin User',
                                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                              fontWeight: FontWeight.w600,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Text(
                                            user?.email ?? '<EMAIL>',
                                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                              color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                    PopupMenuButton<String>(
                                      icon: Icon(
                                        Icons.more_vert,
                                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                      ),
                                      onSelected: (value) async {
                                        switch (value) {
                                          case 'profile':
                                            _showPasswordChangeDialog(context);
                                            break;
                                          case 'admin_settings':
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) => const SettingsScreen(),
                                              ),
                                            );
                                            break;
                                          case 'logout':
                                            final shouldLogout = await showDialog<bool>(
                                              context: context,
                                              builder: (context) => AlertDialog(
                                                title: const Text('Sign Out'),
                                                content: const Text('Are you sure you want to sign out?'),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () => Navigator.pop(context, false),
                                                    child: const Text('Cancel'),
                                                  ),
                                                  TextButton(
                                                    onPressed: () => Navigator.pop(context, true),
                                                    style: TextButton.styleFrom(
                                                      foregroundColor: Theme.of(context).colorScheme.error,
                                                    ),
                                                    child: const Text('Sign Out'),
                                                  ),
                                                ],
                                              ),
                                            );

                                            if (shouldLogout == true) {
                                              try {
                                                await authProvider.signOut();
                                                if (context.mounted) {
                                                  Navigator.of(context).pushReplacementNamed('/login');
                                                }
                                              } catch (e) {
                                                if (context.mounted) {
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    SnackBar(content: Text('Error signing out: $e')),
                                                  );
                                                }
                                              }
                                            }
                                            break;
                                        }
                                      },
                                      itemBuilder: (context) => [
                                        PopupMenuItem(
                                          value: 'profile',
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.person_outline,
                                                size: 20,
                                                color: Theme.of(context).colorScheme.onSurface,
                                              ),
                                              const SizedBox(width: 12),
                                              const Text('Profile'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuDivider(),
                                        PopupMenuItem(
                                          value: 'logout',
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.logout,
                                                size: 20,
                                                color: Theme.of(context).colorScheme.error,
                                              ),
                                              const SizedBox(width: 12),
                                              Text(
                                                'Sign Out',
                                                style: TextStyle(
                                                  color: Theme.of(context).colorScheme.error,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),


          ],
        ),
      ),
    );
  }

  void _showPasswordChangeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const PasswordChangeDialog(),
    );
  }
}
