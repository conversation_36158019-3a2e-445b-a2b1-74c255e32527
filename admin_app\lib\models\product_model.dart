import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  final String id;
  final String sellerId;
  final String sellerName;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String category;
  final List<String> imageUrls;
  final List<String> tags;
  final int stockQuantity;
  final bool isAvailable;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double rating;
  final int reviewCount;
  final Map<String, dynamic> specifications;
  final String? sellerWhatsApp;
  final String? sellerWeChat;
  final String? sellerLocation;
  final String? liveLink;
  final bool isApproved;
  final bool isDeleted;
  final String? moderatorNote;
  final DateTime? deletedAt;

  ProductModel({
    required this.id,
    required this.sellerId,
    required this.sellerName,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.category,
    this.imageUrls = const [],
    this.tags = const [],
    this.stockQuantity = 0,
    this.isAvailable = true,
    this.isFeatured = false,
    required this.createdAt,
    required this.updatedAt,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.specifications = const {},
    this.sellerWhatsApp,
    this.sellerWeChat,
    this.sellerLocation,
    this.liveLink,
    this.isApproved = true,
    this.isDeleted = false,
    this.moderatorNote,
    this.deletedAt,
  });

  // Convert ProductModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'name': name,
      'description': description,
      'price': price,
      'originalPrice': originalPrice,
      'category': category,
      'imageUrls': imageUrls,
      'tags': tags,
      'stockQuantity': stockQuantity,
      'isAvailable': isAvailable,
      'isFeatured': isFeatured,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'rating': rating,
      'reviewCount': reviewCount,
      'specifications': specifications,
      'sellerWhatsApp': sellerWhatsApp,
      'sellerWeChat': sellerWeChat,
      'sellerLocation': sellerLocation,
      'liveLink': liveLink,
      'isApproved': isApproved,
      'isDeleted': isDeleted,
      'moderatorNote': moderatorNote,
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
    };
  }

  // Create ProductModel from Firestore document
  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id'] ?? '',
      sellerId: map['sellerId'] ?? '',
      sellerName: map['sellerName'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      originalPrice: map['originalPrice']?.toDouble(),
      category: map['category'] ?? '',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      stockQuantity: map['stockQuantity'] ?? 0,
      isAvailable: map['isAvailable'] ?? true,
      isFeatured: map['isFeatured'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      rating: (map['rating'] ?? 0).toDouble(),
      reviewCount: map['reviewCount'] ?? 0,
      specifications: Map<String, dynamic>.from(map['specifications'] ?? {}),
      sellerWhatsApp: map['sellerWhatsApp'],
      sellerWeChat: map['sellerWeChat'],
      sellerLocation: map['sellerLocation'],
      liveLink: map['liveLink'],
      isApproved: map['isApproved'] ?? true,
      isDeleted: map['isDeleted'] ?? false,
      moderatorNote: map['moderatorNote'],
      deletedAt: (map['deletedAt'] as Timestamp?)?.toDate(),
    );
  }

  // Create ProductModel from Firestore DocumentSnapshot
  factory ProductModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProductModel.fromMap(data);
  }

  // Create a copy of ProductModel with updated fields
  ProductModel copyWith({
    String? id,
    String? sellerId,
    String? sellerName,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? category,
    List<String>? imageUrls,
    List<String>? tags,
    int? stockQuantity,
    bool? isAvailable,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? rating,
    int? reviewCount,
    Map<String, dynamic>? specifications,
    String? sellerWhatsApp,
    String? sellerWeChat,
    String? sellerLocation,
    String? liveLink,
    bool? isApproved,
    bool? isDeleted,
    String? moderatorNote,
    DateTime? deletedAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      category: category ?? this.category,
      imageUrls: imageUrls ?? this.imageUrls,
      tags: tags ?? this.tags,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      specifications: specifications ?? this.specifications,
      sellerWhatsApp: sellerWhatsApp ?? this.sellerWhatsApp,
      sellerWeChat: sellerWeChat ?? this.sellerWeChat,
      sellerLocation: sellerLocation ?? this.sellerLocation,
      liveLink: liveLink ?? this.liveLink,
      isApproved: isApproved ?? this.isApproved,
      isDeleted: isDeleted ?? this.isDeleted,
      moderatorNote: moderatorNote ?? this.moderatorNote,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  // Helper getters
  bool get hasImages => imageUrls.isNotEmpty;
  String get primaryImageUrl => imageUrls.isNotEmpty ? imageUrls.first : '';
  String get title => name; // Alias for name to match the usage in user_detail_screen

  // Check if product has live link
  bool get hasLiveLink => liveLink != null && liveLink!.isNotEmpty;
  
  // Check if product is on sale
  bool get isOnSale => originalPrice != null && originalPrice! > price;

  // Get discount percentage
  double get discountPercentage {
    if (!isOnSale) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  // Check if product is in stock
  bool get isInStock => stockQuantity > 0;

  // Get formatted price
  String get formattedPrice => '৳${price.toStringAsFixed(2)}';

  // Get formatted original price
  String get formattedOriginalPrice =>
      originalPrice != null ? '৳${originalPrice!.toStringAsFixed(2)}' : '';

  // Get stock status
  String get stockStatus {
    if (stockQuantity <= 0) return 'Out of Stock';
    if (stockQuantity <= 5) return 'Low Stock';
    return 'In Stock';
  }

  // Get rating stars
  String get ratingStars {
    final fullStars = rating.floor();
    final hasHalfStar = (rating - fullStars) >= 0.5;
    String stars = '★' * fullStars;
    if (hasHalfStar) stars += '☆';
    final emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    stars += '☆' * emptyStars;
    return stars;
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, price: $price, sellerId: $sellerId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
