import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/auth_provider.dart';
import '../services/navigation_service.dart';
import '../models/analytics_model.dart';
import '../services/analytics_service.dart';
import '../widgets/dashboard/stats_card.dart';
import '../widgets/dashboard/analytics_chart.dart';
import '../widgets/dashboard/recent_activities.dart';
import '../widgets/password_change_dialog.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import 'main_layout.dart';
import 'user_management_screen.dart';
import 'user_registration_approval_screen.dart';
import 'reseller_applications_screen.dart';
import 'reseller_management_screen.dart';
import 'admin_management_screen.dart';
import 'product_management_screen.dart';
import 'content_moderation_screen.dart';
import 'product_category_management_screen.dart';
import 'support_management_screen.dart';
import 'referral_management_screen.dart';
import 'settings_screen.dart';
import 'pending_products_screen.dart';
import 'slider_management_screen.dart';

class EnhancedDashboardScreen extends StatefulWidget {
  final String? initialRoute;
  const EnhancedDashboardScreen({Key? key, this.initialRoute}) : super(key: key);

  @override
  State<EnhancedDashboardScreen> createState() => _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final TextEditingController _searchController = TextEditingController();
  String _currentRoute = '/dashboard';

  // Analytics data
  AnalyticsModel? _analytics;
  bool _isLoadingAnalytics = true;

  // Real-time streams
  Stream<int>? _userCountStream;
  Stream<int>? _postCountStream;
  Stream<int>? _productCountStream;
  Stream<int>? _resellerCountStream;
  Stream<int>? _pendingApplicationsStream;
  Stream<int>? _pendingRegistrationsStream;

  @override
  void initState() {
    super.initState();
    _currentRoute = widget.initialRoute ?? '/dashboard';
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..forward();
    _initializeData();
  }

  void _initializeData() {
    _loadAnalytics();
    _initializeStreams();
  }

  void _loadAnalytics() async {
    try {
      final analytics = await AnalyticsService.getAnalytics();
      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoadingAnalytics = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAnalytics = false;
        });
      }
      print('Error loading analytics: $e');
    }
  }

  void _initializeStreams() {
    _userCountStream = AnalyticsService.getUserCountStream();
    _postCountStream = AnalyticsService.getPostCountStream();
    _productCountStream = AnalyticsService.getProductCountStream();
    _resellerCountStream = AnalyticsService.getResellerCountStream();
    _pendingApplicationsStream = AnalyticsService.getPendingApplicationsCountStream();
    _pendingRegistrationsStream = AnalyticsService.getPendingRegistrationsCountStream();
  }

  void _onNavigationItemTapped(String route) {
    setState(() {
      _currentRoute = route;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return MainLayout(
      currentRoute: _currentRoute,
      onNavigationItemTapped: _onNavigationItemTapped,
      child: _buildContentForRoute(_currentRoute, theme, context),
    );
  }

  Widget _buildContentForRoute(String route, ThemeData theme, BuildContext context) {
    switch (route) {
      case '/':
      case '/dashboard':
        return _buildDashboardContent(theme, context);
      case '/users':
        return _buildUsersContent(theme);
      case '/resellers':
        return _buildResellersContent(theme);
      case '/pending-registrations':
        return const UserRegistrationApprovalScreen();
      case '/reseller-applications':
        return const ResellerApplicationsScreen();
      case '/admins':
        return _buildAdminsContent(theme);
      case '/products':
        return _buildProductsContent(theme);
      case '/pending-products':
        return const PendingProductsScreen();
      case '/categories':
        return const ProductCategoryManagementScreen();
      case '/support':
        return const SupportManagementScreen();
      case '/sliders':
        return const SliderManagementScreen();
      case '/referrals':
        return const ReferralManagementScreen();
      case '/posts':
      case '/comments':
      case '/moderation':
      case '/moderation/posts':
      case '/moderation/comments':
      case '/moderation/reports':
        return const ContentModerationScreen();
      case '/content':
        return _buildContentManagementContent(theme);
      case '/settings':
        return _buildSettingsContent(theme);
      case '/notifications':
        return _buildNotificationsContent(theme);
      case '/reports':
        return _buildReportsContent(theme);
      default:
        return _buildDashboardContent(theme, context);
    }
  }

  Widget _buildDashboardContent(ThemeData theme, BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.surface,
            theme.colorScheme.surface.withOpacity(0.8),
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEnhancedHeader(theme, context),
            const SizedBox(height: 32),
            _buildEnhancedStatsGrid(theme, context),
            const SizedBox(height: 32),

          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader(ThemeData theme, BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;
    final now = DateTime.now();
    final timeOfDay = now.hour < 12 ? 'Good Morning' : now.hour < 17 ? 'Good Afternoon' : 'Good Evening';

    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.primaryColor.withOpacity(0.1),
            theme.primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isSmallScreen = constraints.maxWidth < 600;

          if (isSmallScreen) {
            // Stack layout for small screens to prevent overflow
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: theme.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.dashboard_rounded,
                              size: 16,
                              color: theme.primaryColor,
                            ),
                            const SizedBox(width: 6),
                            Flexible(
                              child: Text(
                                'Admin Dashboard',
                                style: TextStyle(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.primaryColor.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Badge(
                            backgroundColor: Colors.red,
                            smallSize: 6,
                            child: Icon(
                              Icons.notifications_rounded,
                              color: theme.primaryColor,
                              size: 20,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => _showProfileMenu(context, theme),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [theme.primaryColor, theme.primaryColor.withOpacity(0.7)],
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: CircleAvatar(
                              radius: 18,
                              backgroundColor: Colors.white,
                              child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                                  ? ClipOval(
                                      child: Image.network(
                                        user.profileImageUrl!,
                                        width: 36,
                                        height: 36,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Text(
                                            user.email?.isNotEmpty == true
                                                ? user.email![0].toUpperCase()
                                                : 'A',
                                            style: TextStyle(
                                              color: theme.primaryColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14,
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Text(
                                      user?.email?.isNotEmpty == true
                                          ? user!.email[0].toUpperCase()
                                          : 'A',
                                      style: TextStyle(
                                        color: theme.primaryColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '$timeOfDay, ${user?.displayName ?? 'Admin'}!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  'Today: ${DateFormat('dd MMMM yyyy, EEEE').format(now)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            );
          }

          // Original row layout for larger screens
          return Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.dashboard_rounded,
                                size: 16,
                                color: theme.primaryColor,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Admin Dashboard',
                                style: TextStyle(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '$timeOfDay, ${user?.displayName ?? 'Admin'}!',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Today: ${DateFormat('dd MMMM yyyy, EEEE').format(now)}',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.primaryColor.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Badge(
                      backgroundColor: Colors.red,
                      smallSize: 8,
                      child: Icon(
                        Icons.notifications_rounded,
                        color: theme.primaryColor,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: () => _showProfileMenu(context, theme),
                    child: Container(
                      padding: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [theme.primaryColor, theme.primaryColor.withOpacity(0.7)],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: CircleAvatar(
                        radius: 22,
                        backgroundColor: Colors.white,
                        child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                            ? ClipOval(
                                child: Image.network(
                                  user.profileImageUrl!,
                                  width: 44,
                                  height: 44,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Text(
                                      user.email?.isNotEmpty == true
                                          ? user.email![0].toUpperCase()
                                          : 'A',
                                      style: TextStyle(
                                        color: theme.primaryColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    );
                                  },
                                ),
                              )
                            : Text(
                                user?.email?.isNotEmpty == true
                                    ? user!.email[0].toUpperCase()
                                    : 'A',
                                style: TextStyle(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEnhancedStatsGrid(ThemeData theme, BuildContext context) {
    if (_isLoadingAnalytics) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading data...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Always show 2 cards per row for better layout
        int crossAxisCount = 2;
        double childAspectRatio = constraints.maxWidth > 800 ? 2.5 : 2.2;

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
          childAspectRatio: childAspectRatio,
          children: [
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Total Users',
              stream: _userCountStream,
              fallbackValue: _analytics?.totalUsers.toString() ?? '0',
              icon: Icons.people_rounded,
              color: const Color(0xFF3B82F6),
              gradient: [const Color(0xFF3B82F6), const Color(0xFF1D4ED8)],
              growth: _analytics?.userGrowthRate,
              onTap: () => _onNavigationItemTapped('/users'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Total Posts',
              stream: _postCountStream,
              fallbackValue: _analytics?.totalPosts.toString() ?? '0',
              icon: Icons.article_rounded,
              color: const Color(0xFF10B981),
              gradient: [const Color(0xFF10B981), const Color(0xFF059669)],
              growth: _analytics?.postGrowthRate,
              onTap: () => _onNavigationItemTapped('/posts'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Total Products',
              stream: _productCountStream,
              fallbackValue: _analytics?.totalProducts.toString() ?? '0',
              icon: Icons.inventory_rounded,
              color: const Color(0xFFF59E0B),
              gradient: [const Color(0xFFF59E0B), const Color(0xFFD97706)],
              growth: _analytics?.productGrowthRate,
              onTap: () => _onNavigationItemTapped('/products'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Total Resellers',
              stream: _resellerCountStream,
              fallbackValue: _analytics?.totalResellers.toString() ?? '0',
              icon: Icons.store_rounded,
              color: const Color(0xFF8B5CF6),
              gradient: [const Color(0xFF8B5CF6), const Color(0xFF7C3AED)],
              onTap: () => _onNavigationItemTapped('/resellers'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Pending Registrations',
              stream: _pendingRegistrationsStream,
              fallbackValue: '0',
              icon: Icons.person_add_rounded,
              color: const Color(0xFFFF6B35),
              gradient: [const Color(0xFFFF6B35), const Color(0xFFE55A2B)],
              onTap: () => _onNavigationItemTapped('/pending-registrations'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Reseller Requests',
              stream: _pendingApplicationsStream,
              fallbackValue: _analytics?.pendingResellerApplications.toString() ?? '0',
              icon: Icons.pending_actions_rounded,
              color: const Color(0xFFEF4444),
              gradient: [const Color(0xFFEF4444), const Color(0xFFDC2626)],
              onTap: () => _onNavigationItemTapped('/reseller-applications'),
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Engineer List',
              stream: null,
              fallbackValue: '',
              icon: Icons.engineering_rounded,
              color: const Color(0xFF3B82F6),
              gradient: [const Color(0xFF3B82F6), const Color(0xFF1D4ED8)],
              onTap: _launchEngineerList,
            ),
            _buildEnhancedStatsCard(
              theme: theme,
              title: 'Pending Products',
              stream: null,
              fallbackValue: '',
              icon: Icons.pending_actions_rounded,
              color: const Color(0xFFF59E0B),
              gradient: [const Color(0xFFF59E0B), const Color(0xFFD97706)],
              onTap: () => _onNavigationItemTapped('/pending-products'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEnhancedStatsCard({
    required ThemeData theme,
    required String title,
    required Stream<int>? stream,
    required String fallbackValue,
    required IconData icon,
    required Color color,
    required List<Color> gradient,
    double? growth,
    VoidCallback? onTap,
  }) {
    return StreamBuilder<int>(
      stream: stream,
      builder: (context, snapshot) {
        final value = snapshot.hasData ? snapshot.data.toString() : fallbackValue;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.15), // Increased opacity for better visibility
                color.withOpacity(0.08),
              ],
            ),
            borderRadius: BorderRadius.circular(24), // Increased border radius
            border: Border.all(
              color: color.withOpacity(0.3), // Increased border opacity
              width: 1.5, // Increased border width
            ),
            boxShadow: [ // Added card shadow
              BoxShadow(
                color: color.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24), // Updated to match container border radius
              onTap: onTap,
              child: Padding(
                padding: const EdgeInsets.all(16.0), // Increased padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8), // Increased icon padding
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: gradient,
                            ),
                            borderRadius: BorderRadius.circular(12), // Increased border radius
                            boxShadow: [
                              BoxShadow(
                                color: color.withOpacity(0.4), // Increased shadow opacity
                                blurRadius: 4, // Increased blur radius
                                offset: const Offset(0, 2), // Increased shadow offset
                              ),
                            ],
                          ),
                          child: Icon(
                            icon,
                            color: Colors.white,
                            size: 20, // Increased icon size
                          ),
                        ),
                        if (growth != null)
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: growth >= 0 ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    growth >= 0 ? Icons.trending_up : Icons.trending_down,
                                    size: 10,
                                    color: growth >= 0 ? Colors.green : Colors.red,
                                  ),
                                  const SizedBox(width: 1),
                                  Flexible(
                                    child: Text(
                                      '${growth.abs().toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        color: growth >= 0 ? Colors.green : Colors.red,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 8,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      value,
                      style: theme.textTheme.headlineSmall?.copyWith( // Changed to larger text style
                        fontWeight: FontWeight.bold,
                        color: color,
                        fontSize: 24, // Explicit larger font size
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8), // Increased spacing
                    Text(
                      title,
                      style: theme.textTheme.bodyMedium?.copyWith( // Changed to medium text style
                        color: theme.colorScheme.onSurface.withOpacity(0.8), // Increased opacity
                        fontWeight: FontWeight.w600, // Increased font weight
                        fontSize: 12, // Increased font size
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12), // Added bottom padding
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }













  void _showPasswordChangeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const PasswordChangeDialog(),
    );
  }

  void _showProfileMenu(BuildContext context, ThemeData theme) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: theme.primaryColor.withOpacity(0.1),
                    child: user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty
                        ? ClipOval(
                            child: Image.network(
                              user.profileImageUrl!,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Text(
                                  user.email?.isNotEmpty == true ? user.email![0].toUpperCase() : 'A',
                                  style: theme.textTheme.headlineSmall?.copyWith(
                                    color: theme.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          )
                        : Text(
                            user?.email?.isNotEmpty == true ? user!.email![0].toUpperCase() : 'A',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user?.displayName ?? user?.email ?? 'Admin User',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          user?.email ?? '<EMAIL>',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Administrator',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.person_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              title: Text(
                'Profile Settings',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Manage your account',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              onTap: () {
                Navigator.pop(context);
                _showPasswordChangeDialog(context);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.logout,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
              ),
              title: Text(
                'Sign Out',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Sign out of your account',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              onTap: () async {
                Navigator.pop(context);
                final authProvider = Provider.of<AuthProvider>(context, listen: false);

                final shouldLogout = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Sign Out'),
                    content: const Text('Are you sure you want to sign out?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context, false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context, true),
                        style: TextButton.styleFrom(
                          foregroundColor: Theme.of(context).colorScheme.error,
                        ),
                        child: const Text('Sign Out'),
                      ),
                    ],
                  ),
                );

                if (shouldLogout == true) {
                  try {
                    await authProvider.signOut();
                    if (context.mounted) {
                      Navigator.of(context).pushReplacementNamed('/login');
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error signing out: $e')),
                      );
                    }
                  }
                }
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Content builders for different routes
  Widget _buildUsersContent(ThemeData theme) {
    return const UserManagementScreen();
  }

  Widget _buildResellersContent(ThemeData theme) {
    return const ResellerManagementScreen();
  }

  Widget _buildAdminsContent(ThemeData theme) {
    return const AdminManagementScreen();
  }

  Widget _buildProductsContent(ThemeData theme) {
    return const ProductManagementScreen();
  }

  Widget _buildCategoriesContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category Management',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.category, size: 64, color: theme.colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Category Management System',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Organize products into categories and manage category hierarchy.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Post Management',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.article, size: 64, color: theme.colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Post Management System',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create, edit, and manage posts. Monitor post engagement and interactions.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Comment Management',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.comment, size: 64, color: theme.colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Comment Management System',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Moderate comments, handle reports, and manage user interactions.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentManagementContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Content Management',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.content_copy, size: 64, color: theme.colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Content Management System',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Manage all content, media files, and digital assets.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent(ThemeData theme) {
    return const SettingsScreen();
  }

  Widget _buildNotificationsContent(ThemeData theme) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        if (user == null) {
          return const Center(child: Text('Please log in to view notifications'));
        }

        return StreamBuilder<List<NotificationModel>>(
          stream: NotificationService.getAdminNotificationsStream(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
                    const SizedBox(height: 16),
                    Text('Error loading notifications', style: theme.textTheme.titleLarge),
                  ],
                ),
              );
            }

            final notifications = snapshot.data ?? [];
            final recentNotifications = notifications.take(5).toList();

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Notifications',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pushNamed(context, '/admin-notifications'),
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (recentNotifications.isEmpty)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: Column(
                          children: [
                            Icon(Icons.notifications_none, size: 64, color: theme.colorScheme.primary),
                            const SizedBox(height: 16),
                            Text('No notifications yet', style: theme.textTheme.titleLarge),
                            const SizedBox(height: 8),
                            Text(
                              'Admin notifications will appear here',
                              style: theme.textTheme.bodyMedium,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    ...recentNotifications.map((notification) => Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getNotificationColor(notification.type).withOpacity(0.1),
                          child: Icon(
                            _getNotificationIcon(notification.type),
                            color: _getNotificationColor(notification.type),
                            size: 20,
                          ),
                        ),
                        title: Text(
                          notification.title,
                          style: TextStyle(
                            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          notification.message,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              notification.timeAgo,
                              style: theme.textTheme.bodySmall,
                            ),
                            if (!notification.isRead)
                              Container(
                                width: 8,
                                height: 8,
                                margin: const EdgeInsets.only(top: 4),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        onTap: () => _handleNotificationTap(notification),
                      ),
                    )).toList(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildReportsContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Reports',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/posts');
                },
                icon: const Icon(Icons.arrow_back, size: 18),
                label: const Text('Back to Posts'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade100,
                  foregroundColor: Colors.grey.shade700,
                  elevation: 0,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(Icons.assessment, size: 64, color: theme.colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Reports & Analytics',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Generate and view detailed reports about system performance and usage.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.userRegistration:
        return Icons.person_add;
      case NotificationType.resellerApplication:
        return Icons.business;
      case NotificationType.postReported:
        return Icons.report;
      case NotificationType.userVerification:
        return Icons.verified_user;
      case NotificationType.system:
        return Icons.settings;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.userRegistration:
        return Colors.green;
      case NotificationType.resellerApplication:
        return Colors.blue;
      case NotificationType.postReported:
        return Colors.red;
      case NotificationType.userVerification:
        return Colors.orange;
      case NotificationType.system:
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  void _handleNotificationTap(NotificationModel notification) async {
    // Mark as read
    if (!notification.isRead) {
      await NotificationService.markAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case NotificationType.userRegistration:
        Navigator.pushNamed(context, '/user-management');
        break;
      case NotificationType.resellerApplication:
        Navigator.pushNamed(context, '/reseller-applications');
        break;
      case NotificationType.postReported:
        Navigator.pushNamed(context, '/content-moderation');
        break;
      case NotificationType.userVerification:
        Navigator.pushNamed(context, '/user-management');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Notification: ${notification.title}')),
        );
        break;
    }
  }

  void _navigateToRoute(String route) {
    Navigator.pushNamed(context, route);
  }

  Future<void> _launchEngineerList() async {
    const url = 'https://amalpoint.com/engineer-list/';
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open Engineer List page'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening Engineer List: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
