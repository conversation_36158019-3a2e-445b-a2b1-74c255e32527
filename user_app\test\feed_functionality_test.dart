import 'package:flutter_test/flutter_test.dart';
import 'package:amal_point_app/models/post_model.dart';
import 'package:amal_point_app/models/comment_model.dart';
import 'package:amal_point_app/models/user_model.dart';

void main() {
  group('Feed Functionality Tests', () {
    late UserModel testUser;
    late PostModel testPost;
    late CommentModel testComment;

    setUp(() {
      final now = DateTime.now();
      
      testUser = UserModel(
        id: 'test_user_id',
        email: '<EMAIL>',
        username: 'testuser',
        displayName: 'Test User',
        profileImageUrl: 'https://example.com/profile.jpg',
        coverImageUrl: null,
        bio: 'Test bio',
        gender: 'Male',
        address: 'Test Address',
        country: 'Bangladesh',
        isVerified: false,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        metadata: const {},
      );

      testPost = PostModel(
        id: 'test_post_id',
        userId: testUser.id,
        username: testUser.username,
        userDisplayName: testUser.displayName,
        userProfileImageUrl: testUser.profileImageUrl,
        content: 'This is a test post with some content',
        imageUrls: [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg'
        ],
        createdAt: now,
        updatedAt: now,
        likes: ['user1', 'user2'],
        comments: ['comment1', 'comment2'],
        shares: ['user3'],
      );

      testComment = CommentModel(
        id: 'test_comment_id',
        postId: testPost.id,
        userId: testUser.id,
        username: testUser.username,
        userDisplayName: testUser.displayName,
        userProfileImageUrl: testUser.profileImageUrl,
        content: 'This is a test comment',
        createdAt: now,
        updatedAt: now,
        likes: ['user1'],
      );
    });

    group('Post Model Tests', () {
      test('should create post with correct properties', () {
        expect(testPost.id, equals('test_post_id'));
        expect(testPost.userId, equals(testUser.id));
        expect(testPost.content, equals('This is a test post with some content'));
        expect(testPost.hasImages, isTrue);
        expect(testPost.imageUrls.length, equals(2));
        expect(testPost.likeCount, equals(2));
        expect(testPost.commentCount, equals(2));
        expect(testPost.shareCount, equals(1));
      });

      test('should check if user liked post', () {
        expect(testPost.isLikedBy('user1'), isTrue);
        expect(testPost.isLikedBy('user2'), isTrue);
        expect(testPost.isLikedBy('user3'), isFalse);
      });

      test('should convert to map correctly', () {
        final map = testPost.toMap();
        
        expect(map['id'], equals(testPost.id));
        expect(map['userId'], equals(testPost.userId));
        expect(map['content'], equals(testPost.content));
        expect(map['imageUrls'], equals(testPost.imageUrls));
        expect(map['likes'], equals(testPost.likes));
        expect(map['comments'], equals(testPost.comments));
        expect(map['shares'], equals(testPost.shares));
        expect(map['isActive'], equals(testPost.isActive));
      });

      test('should create copy with updated fields', () {
        final updatedPost = testPost.copyWith(
          content: 'Updated content',
          likes: ['user1', 'user2', 'user3'],
        );

        expect(updatedPost.content, equals('Updated content'));
        expect(updatedPost.likeCount, equals(3));
        expect(updatedPost.id, equals(testPost.id)); // Should remain same
        expect(updatedPost.userId, equals(testPost.userId)); // Should remain same
      });
    });

    group('Comment Model Tests', () {
      test('should create comment with correct properties', () {
        expect(testComment.id, equals('test_comment_id'));
        expect(testComment.postId, equals(testPost.id));
        expect(testComment.userId, equals(testUser.id));
        expect(testComment.content, equals('This is a test comment'));
        expect(testComment.likeCount, equals(1));
      });

      test('should check if user liked comment', () {
        expect(testComment.isLikedBy('user1'), isTrue);
        expect(testComment.isLikedBy('user2'), isFalse);
      });

      test('should convert to map correctly', () {
        final map = testComment.toMap();
        
        expect(map['id'], equals(testComment.id));
        expect(map['postId'], equals(testComment.postId));
        expect(map['userId'], equals(testComment.userId));
        expect(map['content'], equals(testComment.content));
        expect(map['likes'], equals(testComment.likes));
        expect(map['isActive'], equals(testComment.isActive));
      });

      test('should create copy with updated fields', () {
        final updatedComment = testComment.copyWith(
          content: 'Updated comment',
          likes: ['user1', 'user2'],
        );

        expect(updatedComment.content, equals('Updated comment'));
        expect(updatedComment.likeCount, equals(2));
        expect(updatedComment.id, equals(testComment.id)); // Should remain same
        expect(updatedComment.postId, equals(testComment.postId)); // Should remain same
      });
    });

    group('Time Ago Tests', () {
      test('should show correct time ago for posts', () {
        final now = DateTime.now();
        
        // Test "Just now"
        final justNowPost = testPost.copyWith(createdAt: now);
        expect(justNowPost.timeAgo, equals('Just now'));

        // Test minutes ago
        final minutesAgoPost = testPost.copyWith(
          createdAt: now.subtract(const Duration(minutes: 30))
        );
        expect(minutesAgoPost.timeAgo, equals('30m ago'));

        // Test hours ago
        final hoursAgoPost = testPost.copyWith(
          createdAt: now.subtract(const Duration(hours: 2))
        );
        expect(hoursAgoPost.timeAgo, equals('2h ago'));

        // Test days ago
        final daysAgoPost = testPost.copyWith(
          createdAt: now.subtract(const Duration(days: 3))
        );
        expect(daysAgoPost.timeAgo, equals('3d ago'));
      });

      test('should show correct time ago for comments', () {
        final now = DateTime.now();
        
        // Test "Just now"
        final justNowComment = testComment.copyWith(createdAt: now);
        expect(justNowComment.timeAgo, equals('Just now'));

        // Test minutes ago
        final minutesAgoComment = testComment.copyWith(
          createdAt: now.subtract(const Duration(minutes: 15))
        );
        expect(minutesAgoComment.timeAgo, equals('15m ago'));

        // Test hours ago
        final hoursAgoComment = testComment.copyWith(
          createdAt: now.subtract(const Duration(hours: 1))
        );
        expect(hoursAgoComment.timeAgo, equals('1h ago'));

        // Test days ago
        final daysAgoComment = testComment.copyWith(
          createdAt: now.subtract(const Duration(days: 1))
        );
        expect(daysAgoComment.timeAgo, equals('1d ago'));
      });
    });

    group('Post Interaction Tests', () {
      test('should handle empty content and images', () {
        final emptyPost = PostModel(
          id: 'empty_post',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(emptyPost.content.isEmpty, isTrue);
        expect(emptyPost.hasImages, isFalse);
        expect(emptyPost.likeCount, equals(0));
        expect(emptyPost.commentCount, equals(0));
        expect(emptyPost.shareCount, equals(0));
      });

      test('should handle post with only images', () {
        final imageOnlyPost = PostModel(
          id: 'image_post',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: '',
          imageUrls: ['https://example.com/image.jpg'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(imageOnlyPost.content.isEmpty, isTrue);
        expect(imageOnlyPost.hasImages, isTrue);
        expect(imageOnlyPost.imageUrls.length, equals(1));
      });

      test('should handle post with only content', () {
        final textOnlyPost = PostModel(
          id: 'text_post',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'This is a text-only post',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(textOnlyPost.content.isNotEmpty, isTrue);
        expect(textOnlyPost.hasImages, isFalse);
        expect(textOnlyPost.imageUrls.isEmpty, isTrue);
      });
    });

    group('Model Equality Tests', () {
      test('should check post equality correctly', () {
        final post1 = PostModel(
          id: 'same_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Content 1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final post2 = PostModel(
          id: 'same_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Content 2', // Different content
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final post3 = PostModel(
          id: 'different_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Content 1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(post1 == post2, isTrue); // Same ID
        expect(post1 == post3, isFalse); // Different ID
        expect(post1.hashCode, equals(post2.hashCode)); // Same hash for same ID
      });

      test('should check comment equality correctly', () {
        final comment1 = CommentModel(
          id: 'same_id',
          postId: 'post_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Comment 1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final comment2 = CommentModel(
          id: 'same_id',
          postId: 'post_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Comment 2', // Different content
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final comment3 = CommentModel(
          id: 'different_id',
          postId: 'post_id',
          userId: 'user_id',
          username: 'username',
          userDisplayName: 'User Name',
          content: 'Comment 1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(comment1 == comment2, isTrue); // Same ID
        expect(comment1 == comment3, isFalse); // Different ID
        expect(comment1.hashCode, equals(comment2.hashCode)); // Same hash for same ID
      });
    });
  });
}
