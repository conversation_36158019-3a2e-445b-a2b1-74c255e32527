import 'package:cloud_firestore/cloud_firestore.dart';

enum ReferralStatus {
  pending,
  completed,
  expired,
  cancelled,
}

extension ReferralStatusExtension on ReferralStatus {
  String get value {
    switch (this) {
      case ReferralStatus.pending:
        return 'pending';
      case ReferralStatus.completed:
        return 'completed';
      case ReferralStatus.expired:
        return 'expired';
      case ReferralStatus.cancelled:
        return 'cancelled';
    }
  }

  static ReferralStatus fromString(String value) {
    switch (value) {
      case 'pending':
        return ReferralStatus.pending;
      case 'completed':
        return ReferralStatus.completed;
      case 'expired':
        return ReferralStatus.expired;
      case 'cancelled':
        return ReferralStatus.cancelled;
      default:
        return ReferralStatus.pending;
    }
  }
}

class ReferralModel {
  final String id;
  final String referrerId; // User who made the referral
  final String refereeId; // User who was referred
  final String referralCode;
  final ReferralStatus status;
  final double rewardAmount;
  final String? rewardType; // 'cash', 'points', 'discount'
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final Map<String, dynamic> metadata;

  // Referrer information
  final String referrerName;
  final String referrerEmail;
  
  // Referee information
  final String refereeName;
  final String refereeEmail;

  ReferralModel({
    required this.id,
    required this.referrerId,
    required this.refereeId,
    required this.referralCode,
    required this.status,
    required this.rewardAmount,
    this.rewardType,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
    this.expiresAt,
    this.metadata = const {},
    required this.referrerName,
    required this.referrerEmail,
    required this.refereeName,
    required this.refereeEmail,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'referrerId': referrerId,
      'refereeId': refereeId,
      'referralCode': referralCode,
      'status': status.value,
      'rewardAmount': rewardAmount,
      'rewardType': rewardType,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'metadata': metadata,
      'referrerName': referrerName,
      'referrerEmail': referrerEmail,
      'refereeName': refereeName,
      'refereeEmail': refereeEmail,
    };
  }

  // Create ReferralModel from Firestore document
  factory ReferralModel.fromMap(Map<String, dynamic> map) {
    return ReferralModel(
      id: map['id'] ?? '',
      referrerId: map['referrerId'] ?? '',
      refereeId: map['refereeId'] ?? '',
      referralCode: map['referralCode'] ?? '',
      status: ReferralStatusExtension.fromString(map['status'] ?? 'pending'),
      rewardAmount: (map['rewardAmount'] ?? 0.0).toDouble(),
      rewardType: map['rewardType'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      completedAt: (map['completedAt'] as Timestamp?)?.toDate(),
      expiresAt: (map['expiresAt'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      referrerName: map['referrerName'] ?? '',
      referrerEmail: map['referrerEmail'] ?? '',
      refereeName: map['refereeName'] ?? '',
      refereeEmail: map['refereeEmail'] ?? '',
    );
  }

  // Create ReferralModel from Firestore DocumentSnapshot
  factory ReferralModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReferralModel.fromMap(data);
  }

  // Copy with method for updates
  ReferralModel copyWith({
    String? id,
    String? referrerId,
    String? refereeId,
    String? referralCode,
    ReferralStatus? status,
    double? rewardAmount,
    String? rewardType,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    Map<String, dynamic>? metadata,
    String? referrerName,
    String? referrerEmail,
    String? refereeName,
    String? refereeEmail,
  }) {
    return ReferralModel(
      id: id ?? this.id,
      referrerId: referrerId ?? this.referrerId,
      refereeId: refereeId ?? this.refereeId,
      referralCode: referralCode ?? this.referralCode,
      status: status ?? this.status,
      rewardAmount: rewardAmount ?? this.rewardAmount,
      rewardType: rewardType ?? this.rewardType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      metadata: metadata ?? this.metadata,
      referrerName: referrerName ?? this.referrerName,
      referrerEmail: referrerEmail ?? this.referrerEmail,
      refereeName: refereeName ?? this.refereeName,
      refereeEmail: refereeEmail ?? this.refereeEmail,
    );
  }

  @override
  String toString() {
    return 'ReferralModel(id: $id, referralCode: $referralCode, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReferralModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
