# Admin App Deployment Instructions

## Cloud Functions Setup

### Prerequisites
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Initialize Firebase project: `firebase init`

### Deploy Cloud Functions

1. Navigate to functions directory:
   ```bash
   cd functions
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build TypeScript:
   ```bash
   npm run build
   ```

4. Deploy functions:
   ```bash
   firebase deploy --only functions
   ```

### Available Cloud Functions

1. **changeUserPassword**: Changes user password using Firebase Admin SDK
   - Parameters: `userId`, `newPassword`
   - Requires admin authentication

2. **forcePasswordReset**: Forces password reset for a user
   - Parameters: `userId`
   - Requires admin authentication

## Features Implemented

### 1. Password Change Functionality
- ✅ Cloud Functions implementation for secure password changes
- ✅ Fallback method for when Cloud Functions are not available
- ✅ Admin logging for password changes

### 2. User Deactivation
- ✅ Active/Inactive status filter in user management
- ✅ Deactivate user with reason
- ✅ Reactivate user functionality
- ✅ Visual status indicators in user cards

### 3. Enhanced User Details
- ✅ Mobile field display
- ✅ Referrer information section
- ✅ Complete referrer details with fallback

## Usage Instructions

### Password Change
1. Go to User Management
2. Select a user
3. Choose "Change Password" from actions
4. Enter new password
5. System will use Cloud Functions if available, otherwise fallback method

### User Deactivation
1. Go to User Management
2. Use "Active Status" filter to view active/inactive users
3. Select "Deactivate" from user actions
4. Provide reason for deactivation
5. User will be marked as inactive

### View User Details
1. Click "View Details" on any user
2. See complete user information including:
   - Mobile number
   - Referrer information (if available)
   - All other user details

## Notes
- Cloud Functions provide the most secure password change method
- Fallback methods are available for development/testing
- All admin actions are logged for audit purposes
