import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
admin.initializeApp();

// Change user password function
export const changeUserPassword = functions.https.onCall(async (data, context) => {
  // Check if the request is from an authenticated admin
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if the user has admin privileges
  const adminUser = await admin.firestore()
    .collection('users')
    .doc(context.auth.uid)
    .get();

  if (!adminUser.exists || adminUser.data()?.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can change user passwords');
  }

  const { userId, newPassword } = data;

  if (!userId || !newPassword) {
    throw new functions.https.HttpsError('invalid-argument', 'userId and newPassword are required');
  }

  if (newPassword.length < 6) {
    throw new functions.https.HttpsError('invalid-argument', 'Password must be at least 6 characters long');
  }

  try {
    // Update the user's password using Firebase Admin SDK
    await admin.auth().updateUser(userId, {
      password: newPassword
    });

    // Update user document in Firestore to track the change
    await admin.firestore()
      .collection('users')
      .doc(userId)
      .update({
        passwordChangedByAdmin: true,
        passwordChangedAt: admin.firestore.FieldValue.serverTimestamp(),
        passwordChangeRequired: false,
        tempPassword: admin.firestore.FieldValue.delete(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    // Log the password change
    await admin.firestore()
      .collection('admin_logs')
      .add({
        action: 'password_changed',
        adminId: context.auth.uid,
        targetUserId: userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: 'Password changed by admin'
      });

    return { success: true, message: 'Password updated successfully' };
  } catch (error) {
    console.error('Error changing user password:', error);
    throw new functions.https.HttpsError('internal', 'Failed to change password');
  }
});

// Force password reset function
export const forcePasswordReset = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const adminUser = await admin.firestore()
    .collection('users')
    .doc(context.auth.uid)
    .get();

  if (!adminUser.exists || adminUser.data()?.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can force password reset');
  }

  const { userId } = data;

  if (!userId) {
    throw new functions.https.HttpsError('invalid-argument', 'userId is required');
  }

  try {
    // Get user data
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(userId)
      .get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data();
    const userEmail = userData?.email;

    if (!userEmail) {
      throw new functions.https.HttpsError('invalid-argument', 'User email not found');
    }

    // Generate password reset link
    const resetLink = await admin.auth().generatePasswordResetLink(userEmail);

    // Update user document
    await admin.firestore()
      .collection('users')
      .doc(userId)
      .update({
        forcePasswordReset: true,
        passwordResetInitiatedAt: admin.firestore.FieldValue.serverTimestamp(),
        passwordResetLink: resetLink,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    // Log the action
    await admin.firestore()
      .collection('admin_logs')
      .add({
        action: 'password_reset_forced',
        adminId: context.auth.uid,
        targetUserId: userId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: 'Password reset forced by admin'
      });

    return { 
      success: true, 
      message: 'Password reset initiated successfully',
      resetLink: resetLink
    };
  } catch (error) {
    console.error('Error forcing password reset:', error);
    throw new functions.https.HttpsError('internal', 'Failed to force password reset');
  }
});
