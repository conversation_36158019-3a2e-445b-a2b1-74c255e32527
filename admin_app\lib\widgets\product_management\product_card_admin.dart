import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_constants.dart';
import '../../models/product_model.dart';
import '../../services/product_service.dart';

class ProductCardAdmin extends StatefulWidget {
  final ProductModel product;
  final VoidCallback? onProductUpdated;
  final bool isSelectionMode;
  final bool isSelected;
  final Function(bool)? onSelectionChanged;
  final VoidCallback? onEdit;

  const ProductCardAdmin({
    super.key,
    required this.product,
    this.onProductUpdated,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onSelectionChanged,
    this.onEdit,
  });

  @override
  State<ProductCardAdmin> createState() => _ProductCardAdminState();
}

class _ProductCardAdminState extends State<ProductCardAdmin> {
  bool _isUpdating = false;
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: _isExpanded ? 4 : 2,
      margin: EdgeInsets.zero,
      shadowColor: AppConstants.primaryColor.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        side: widget.isSelected
            ? const BorderSide(color: AppConstants.primaryColor, width: 2)
            : BorderSide(color: AppConstants.borderColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: _buildHeader(),
          ),
          if (_isExpanded) ...[
            _buildContent(),
            if (widget.product.imageUrls.isNotEmpty) _buildImages(),
            _buildPriceAndStock(),
            _buildStats(),
            _buildExpandedDetails(),
          ] else ...[
            _buildCompactInfo(),
          ],
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: _isExpanded
            ? AppConstants.primaryColor.withOpacity(0.05)
            : Colors.transparent,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConstants.borderRadiusMedium),
          topRight: Radius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.product.name,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: AppConstants.fontSizeLarge,
                    color: _isExpanded
                        ? AppConstants.primaryColor
                        : AppConstants.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: 14,
                      color: AppConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        widget.product.sellerName,
                        style: const TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.category_outlined,
                      size: 14,
                      color: AppConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.product.category,
                      style: const TextStyle(
                        color: AppConstants.textSecondaryColor,
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            children: [
              Row(
                children: [
                  Column(
                    children: [
                      _buildAvailabilityBadge(),
                      const SizedBox(height: 4),
                      if (widget.product.isFeatured) _buildFeaturedBadge(),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppConstants.textSecondaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 4),
                  _buildThreeDotMenu(),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }



  Widget _buildAvailabilityBadge() {
    final isAvailable = widget.product.isAvailable;
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: isAvailable 
            ? AppConstants.successColor.withOpacity(0.1)
            : AppConstants.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(
          color: isAvailable 
              ? AppConstants.successColor.withOpacity(0.3)
              : AppConstants.errorColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isAvailable ? Icons.check_circle : Icons.block,
            size: 12,
            color: isAvailable ? AppConstants.successColor : AppConstants.errorColor,
          ),
          const SizedBox(width: 4),
          Text(
            isAvailable ? 'Available' : 'Unavailable',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
              color: isAvailable ? AppConstants.successColor : AppConstants.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: AppConstants.warningColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: AppConstants.warningColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.star,
            size: 12,
            color: AppConstants.warningColor,
          ),
          const SizedBox(width: 4),
          Text(
            'Featured',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
              color: AppConstants.warningColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThreeDotMenu() {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleMenuAction(value),
      icon: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: AppConstants.textSecondaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Icon(
          Icons.more_vert,
          size: 20,
          color: AppConstants.textSecondaryColor,
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16, color: AppConstants.primaryColor),
              const SizedBox(width: 8),
              const Text('Edit Product'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'toggle_availability',
          child: Row(
            children: [
              Icon(
                widget.product.isAvailable ? Icons.block : Icons.check_circle,
                size: 16,
                color: widget.product.isAvailable
                    ? AppConstants.warningColor
                    : AppConstants.successColor,
              ),
              const SizedBox(width: 8),
              Text(widget.product.isAvailable ? 'Make Unavailable' : 'Make Available'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'toggle_featured',
          child: Row(
            children: [
              Icon(
                widget.product.isFeatured ? Icons.star_border : Icons.star,
                size: 16,
                color: AppConstants.warningColor,
              ),
              const SizedBox(width: 8),
              Text(widget.product.isFeatured ? 'Remove Featured' : 'Make Featured'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: AppConstants.errorColor),
              const SizedBox(width: 8),
              const Text('Delete Product'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.product.description,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          if (widget.product.tags.isNotEmpty) ...[
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: widget.product.tags.take(5).map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
          ],
          Text(
            'Created: ${_formatDate(widget.product.createdAt)}',
            style: const TextStyle(
              color: AppConstants.textSecondaryColor,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
          if (widget.product.moderatorNote != null) ...[
            const SizedBox(height: AppConstants.paddingSmall),
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              decoration: BoxDecoration(
                color: AppConstants.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: AppConstants.warningColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.note_alt,
                    size: 16,
                    color: AppConstants.warningColor,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      'Moderator Note: ${widget.product.moderatorNote}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.warningColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImages() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.product.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            width: 120,
            margin: EdgeInsets.only(
              right: index < widget.product.imageUrls.length - 1
                  ? AppConstants.paddingSmall
                  : 0,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              child: CachedNetworkImage(
                imageUrl: widget.product.imageUrls[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppConstants.backgroundColor,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.error,
                    color: AppConstants.errorColor,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPriceAndStock() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '৳${widget.product.price.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeLarge,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    if (widget.product.originalPrice != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        '৳${widget.product.originalPrice!.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          decoration: TextDecoration.lineThrough,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Stock: ${widget.product.stockQuantity}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          if (widget.product.rating > 0) ...[
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: AppConstants.warningColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.product.rating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                  ],
                ),
                Text(
                  '${widget.product.reviewCount} reviews',
                  style: const TextStyle(
                    color: AppConstants.textSecondaryColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStats() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Row(
        children: [
          _buildStatItem(
            icon: Icons.visibility,
            label: 'Views',
            value: '0', // This would come from analytics
          ),
          const SizedBox(width: AppConstants.paddingLarge),
          _buildStatItem(
            icon: Icons.shopping_cart,
            label: 'Orders',
            value: '0', // This would come from orders
          ),
          const SizedBox(width: AppConstants.paddingLarge),
          _buildStatItem(
            icon: Icons.favorite,
            label: 'Likes',
            value: '0', // This would come from likes
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppConstants.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeSmall,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            color: AppConstants.textSecondaryColor,
            fontSize: AppConstants.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppConstants.borderColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '৳${widget.product.price.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: widget.product.stockQuantity > 0
                        ? AppConstants.successColor.withOpacity(0.1)
                        : AppConstants.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 12,
                        color: widget.product.stockQuantity > 0
                            ? AppConstants.successColor
                            : AppConstants.errorColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.product.stockQuantity}',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          fontWeight: FontWeight.w500,
                          color: widget.product.stockQuantity > 0
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Text(
            'Tap to expand',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor.withOpacity(0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedDetails() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Additional Details',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          _buildDetailRow('Created', _formatDate(widget.product.createdAt)),
          _buildDetailRow('Updated', _formatDate(widget.product.updatedAt)),
          if (widget.product.sellerWhatsApp != null)
            _buildDetailRow('WhatsApp', widget.product.sellerWhatsApp!),
          if (widget.product.sellerWeChat != null)
            _buildDetailRow('WeChat', widget.product.sellerWeChat!),
          if (widget.product.sellerLocation != null)
            _buildDetailRow('Location', widget.product.sellerLocation!),
          if (widget.product.liveLink != null)
            _buildDetailRow('Live Link', widget.product.liveLink!),
          if (widget.product.tags.isNotEmpty)
            _buildDetailRow('Tags', widget.product.tags.join(', ')),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: FontWeight.w500,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    // Since we're only showing approved products and actions are now in the three-dot menu,
    // we can remove the action buttons section entirely
    return const SizedBox.shrink();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        if (widget.onEdit != null) {
          widget.onEdit!();
        }
        break;
      case 'toggle_availability':
        _toggleAvailability();
        break;
      case 'toggle_featured':
        _toggleFeatured();
        break;
      case 'delete':
        _deleteProduct();
        break;
    }
  }

  Future<void> _approveProduct() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ProductService.approveProduct(
        productId: widget.product.id,
        moderatorNote: 'Approved by admin',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product approved successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onProductUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error approving product: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _rejectProduct() async {
    final TextEditingController noteController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Product'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Are you sure you want to reject this product?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'Rejection reason (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ProductService.rejectProduct(
        productId: widget.product.id,
        moderatorNote: result.isNotEmpty ? result : 'Rejected by admin',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product rejected successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onProductUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rejecting product: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _toggleAvailability() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ProductService.updateProductAvailability(
        productId: widget.product.id,
        isAvailable: !widget.product.isAvailable,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.product.isAvailable
                  ? 'Product made unavailable successfully'
                  : 'Product made available successfully',
            ),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onProductUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating product availability: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _toggleFeatured() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ProductService.updateProductFeaturedStatus(
        productId: widget.product.id,
        isFeatured: !widget.product.isFeatured,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.product.isFeatured
                  ? 'Product unfeatured successfully'
                  : 'Product featured successfully',
            ),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onProductUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating product featured status: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _deleteProduct() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: const Text(
          'Are you sure you want to delete this product? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ProductService.deleteProduct(widget.product.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product deleted successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        widget.onProductUpdated?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting product: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
