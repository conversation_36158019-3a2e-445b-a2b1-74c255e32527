import 'package:cloud_firestore/cloud_firestore.dart';
import '../enums/user_role.dart';

class UserModel {
  final String id;
  final String email;
  final String username;
  final String displayName;
  final String? profileImageUrl;
  final String? coverImageUrl;
  final String? bio;
  final String? gender;
  final String? mobile;
  final String? address;
  final String? country;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> followers;
  final List<String> following;
  final bool isVerified;
  final bool isActive;
  final DateTime? lastSeen;
  final DateTime? lastLoginAt;
  final bool isOnline;
  final UserRole role;
  final ResellerApplicationStatus resellerApplicationStatus;
  final DateTime? resellerApplicationDate;
  final String? resellerApplicationReason;
  final RegistrationStatus registrationStatus;
  final String? registrationRejectionReason;

  // Enhanced Reseller Registration Fields
  final String? resellerName;
  final String? resellerReferId;
  final String? resellerNumber;
  final String? resellerAddress;
  final String? resellerNationalIdUrl;
  final String? resellerTradeLicenseUrl;
  final String? resellerSocialMediaLink;

  // Referrer Information
  final String? referrerName;
  final String? referrerNumber;
  final String? referrerAddress;
  final String? referrerNationalIdUrl;

  // Additional metadata
  final Map<String, dynamic> metadata;

  UserModel({
    required this.id,
    required this.email,
    required this.username,
    required this.displayName,
    this.profileImageUrl,
    this.coverImageUrl,
    this.bio,
    this.gender,
    this.mobile,
    this.address,
    this.country,
    required this.createdAt,
    required this.updatedAt,
    this.followers = const [],
    this.following = const [],
    this.isVerified = false,
    this.isActive = true,
    this.lastSeen,
    this.lastLoginAt,
    this.isOnline = false,
    this.role = UserRole.user,
    this.resellerApplicationStatus = ResellerApplicationStatus.none,
    this.resellerApplicationDate,
    this.resellerApplicationReason,
    this.registrationStatus = RegistrationStatus.pending,
    this.registrationRejectionReason,

    // Enhanced Reseller Registration Fields
    this.resellerName,
    this.resellerReferId,
    this.resellerNumber,
    this.resellerAddress,
    this.resellerNationalIdUrl,
    this.resellerTradeLicenseUrl,
    this.resellerSocialMediaLink,

    // Referrer Information
    this.referrerName,
    this.referrerNumber,
    this.referrerAddress,
    this.referrerNationalIdUrl,

    // Additional metadata
    required this.metadata,
  });

  // Convert UserModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'displayName': displayName,
      'profileImageUrl': profileImageUrl,
      'coverImageUrl': coverImageUrl,
      'bio': bio,
      'gender': gender,
      'mobile': mobile,
      'address': address,
      'country': country,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'followers': followers,
      'following': following,
      'isVerified': isVerified,
      'isActive': isActive,
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isOnline': isOnline,
      'role': role.value,
      'resellerApplicationStatus': resellerApplicationStatus.value,
      'resellerApplicationDate': resellerApplicationDate != null ? Timestamp.fromDate(resellerApplicationDate!) : null,
      'resellerApplicationReason': resellerApplicationReason,
      'registrationStatus': registrationStatus.value,
      'registrationRejectionReason': registrationRejectionReason,

      // Enhanced Reseller Registration Fields
      'resellerName': resellerName,
      'resellerReferId': resellerReferId,
      'resellerNumber': resellerNumber,
      'resellerAddress': resellerAddress,
      'resellerNationalIdUrl': resellerNationalIdUrl,
      'resellerTradeLicenseUrl': resellerTradeLicenseUrl,
      'resellerSocialMediaLink': resellerSocialMediaLink,

      // Referrer Information
      'referrerName': referrerName,
      'referrerNumber': referrerNumber,
      'referrerAddress': referrerAddress,
      'referrerNationalIdUrl': referrerNationalIdUrl,

      // Additional metadata
      'metadata': metadata,
    };
  }

  // Create UserModel from Firestore document
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      username: map['username'] ?? '',
      displayName: map['displayName'] ?? '',
      profileImageUrl: map['profileImageUrl'],
      coverImageUrl: map['coverImageUrl'],
      bio: map['bio'],
      gender: map['gender'],
      mobile: map['mobile'],
      address: map['address'],
      country: map['country'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      followers: List<String>.from(map['followers'] ?? []),
      following: List<String>.from(map['following'] ?? []),
      isVerified: map['isVerified'] ?? false,
      isActive: map['isActive'] ?? true,
      lastSeen: (map['lastSeen'] as Timestamp?)?.toDate(),
      lastLoginAt: (map['lastLoginAt'] as Timestamp?)?.toDate(),
      isOnline: map['isOnline'] ?? false,
      role: UserRole.fromString(map['role'] ?? 'user'),
      resellerApplicationStatus: ResellerApplicationStatus.fromString(map['resellerApplicationStatus'] ?? 'none'),
      resellerApplicationDate: (map['resellerApplicationDate'] as Timestamp?)?.toDate(),
      resellerApplicationReason: map['resellerApplicationReason'],
      registrationStatus: RegistrationStatus.fromString(map['registrationStatus'] ?? 'pending'),
      registrationRejectionReason: map['registrationRejectionReason'],

      // Enhanced Reseller Registration Fields
      resellerName: map['resellerName'],
      resellerReferId: map['resellerReferId'],
      resellerNumber: map['resellerNumber'],
      resellerAddress: map['resellerAddress'],
      resellerNationalIdUrl: map['resellerNationalIdUrl'],
      resellerTradeLicenseUrl: map['resellerTradeLicenseUrl'],
      resellerSocialMediaLink: map['resellerSocialMediaLink'],

      // Referrer Information
      referrerName: map['referrerName'],
      referrerNumber: map['referrerNumber'],
      referrerAddress: map['referrerAddress'],
      referrerNationalIdUrl: map['referrerNationalIdUrl'],

      // Additional metadata
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  // Create UserModel from Firestore DocumentSnapshot
  factory UserModel.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromMap(data);
  }

  // Create a copy of UserModel with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? profileImageUrl,
    String? coverImageUrl,
    String? bio,
    String? gender,
    String? mobile,
    String? address,
    String? country,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? followers,
    List<String>? following,
    bool? isVerified,
    bool? isActive,
    DateTime? lastSeen,
    DateTime? lastLoginAt,
    bool? isOnline,
    UserRole? role,
    ResellerApplicationStatus? resellerApplicationStatus,
    DateTime? resellerApplicationDate,
    String? resellerApplicationReason,
    RegistrationStatus? registrationStatus,
    String? registrationRejectionReason,

    // Enhanced Reseller Registration Fields
    String? resellerName,
    String? resellerReferId,
    String? resellerNumber,
    String? resellerAddress,
    String? resellerNationalIdUrl,
    String? resellerTradeLicenseUrl,
    String? resellerSocialMediaLink,

    // Referrer Information
    String? referrerName,
    String? referrerNumber,
    String? referrerAddress,
    String? referrerNationalIdUrl,

    // Additional metadata
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      bio: bio ?? this.bio,
      gender: gender ?? this.gender,
      mobile: mobile ?? this.mobile,
      address: address ?? this.address,
      country: country ?? this.country,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      followers: followers ?? this.followers,
      following: following ?? this.following,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      lastSeen: lastSeen ?? this.lastSeen,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isOnline: isOnline ?? this.isOnline,
      role: role ?? this.role,
      resellerApplicationStatus: resellerApplicationStatus ?? this.resellerApplicationStatus,
      resellerApplicationDate: resellerApplicationDate ?? this.resellerApplicationDate,
      resellerApplicationReason: resellerApplicationReason ?? this.resellerApplicationReason,
      registrationStatus: registrationStatus ?? this.registrationStatus,
      registrationRejectionReason: registrationRejectionReason ?? this.registrationRejectionReason,

      // Enhanced Reseller Registration Fields
      resellerName: resellerName ?? this.resellerName,
      resellerReferId: resellerReferId ?? this.resellerReferId,
      resellerNumber: resellerNumber ?? this.resellerNumber,
      resellerAddress: resellerAddress ?? this.resellerAddress,
      resellerNationalIdUrl: resellerNationalIdUrl ?? this.resellerNationalIdUrl,
      resellerTradeLicenseUrl: resellerTradeLicenseUrl ?? this.resellerTradeLicenseUrl,
      resellerSocialMediaLink: resellerSocialMediaLink ?? this.resellerSocialMediaLink,

      // Referrer Information
      referrerName: referrerName ?? this.referrerName,
      referrerNumber: referrerNumber ?? this.referrerNumber,
      referrerAddress: referrerAddress ?? this.referrerAddress,
      referrerNationalIdUrl: referrerNationalIdUrl ?? this.referrerNationalIdUrl,

      // Additional metadata
      metadata: metadata ?? this.metadata,
    );
  }

  // Get follower count
  int get followerCount => followers.length;

  // Get following count
  int get followingCount => following.length;

  // Check if user is following another user
  bool isFollowing(String userId) => following.contains(userId);

  // Check if user is followed by another user
  bool isFollowedBy(String userId) => followers.contains(userId);

  // Check if user is currently online (online or seen within last 5 minutes)
  bool get isCurrentlyOnline {
    if (isOnline) return true;
    if (lastSeen == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastSeen!);
    return difference.inMinutes <= 5;
  }

  // Get last seen text
  String get lastSeenText {
    if (isOnline) return 'Online';
    if (lastSeen == null) return 'Last seen long ago';

    final now = DateTime.now();
    final difference = now.difference(lastSeen!);

    if (difference.inMinutes < 1) {
      return 'Last seen just now';
    } else if (difference.inMinutes < 60) {
      return 'Last seen ${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return 'Last seen ${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return 'Last seen ${difference.inDays}d ago';
    } else {
      return 'Last seen ${lastSeen!.day}/${lastSeen!.month}/${lastSeen!.year}';
    }
  }

  // Helper methods for role checking
  bool get isUser => role.isUser;
  bool get isReseller => role.isReseller;
  bool get isAdmin => role.isAdmin;

  bool get canAccessProductDashboard => role.canAccessProductDashboard;
  bool get canUploadProducts => role.canUploadProducts;
  bool get canManageUsers => role.canManageUsers;
  bool get canApproveResellers => role.canApproveResellers;

  bool get canApplyForReseller => isUser && resellerApplicationStatus.isNone;
  bool get hasResellerApplicationPending => resellerApplicationStatus.isPending;
  bool get isResellerApplicationApproved => resellerApplicationStatus.isApproved;
  bool get isResellerApplicationRejected => resellerApplicationStatus.isRejected;

  // Registration status helpers
  bool get isRegistrationPending => registrationStatus.isPending;
  bool get isRegistrationApproved => registrationStatus.isApproved;
  bool get isRegistrationRejected => registrationStatus.isRejected;
  bool get canAccessApp => registrationStatus.isApproved;

  String get roleDisplayName => role.displayName;
  String get resellerApplicationStatusDisplayName => resellerApplicationStatus.displayName;
  String get registrationStatusDisplayName => registrationStatus.displayName;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, username: $username, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
